"""
测试关键词统计数据合并的性能
"""
import sys
import os
import time
import random
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wb_api_client import WildberriesAPIClient
from models import KeywordStats


def generate_test_data(num_periods: int, keywords_per_period: int) -> list:
    """生成测试数据"""
    keywords_pool = [
        '手机壳', '保护套', '钢化膜', '充电器', '数据线', '耳机', '音响', '键盘', '鼠标', '显示器',
        '笔记本', '平板', '手机', '相机', '镜头', '三脚架', '背包', '钱包', '手表', '眼镜',
        '帽子', '衣服', '鞋子', '袜子', '内衣', '外套', '裤子', '裙子', '包包', '首饰'
    ]
    
    test_data = []
    
    for period in range(num_periods):
        period_data = {'keywords': []}
        
        for _ in range(keywords_per_period):
            keyword = random.choice(keywords_pool)
            # 添加一些随机后缀使关键词更多样化
            if random.random() < 0.3:
                keyword += f" {random.choice(['苹果', '华为', '小米', '三星', '透明', '黑色', '白色'])}"
            
            keyword_data = {
                'keyword': keyword,
                'views': random.randint(10, 10000),
                'clicks': random.randint(1, 500),
                'sum': round(random.uniform(5.0, 1000.0), 2),
                'ctr': round(random.uniform(0.1, 15.0), 2)
            }
            period_data['keywords'].append(keyword_data)
        
        test_data.append(period_data)
    
    return test_data


def test_performance():
    """性能测试"""
    
    client = WildberriesAPIClient("test_api_key")
    
    print("=" * 60)
    print("关键词统计数据合并性能测试")
    print("=" * 60)
    
    # 测试不同规模的数据
    test_cases = [
        (10, 50),      # 10个时间段，每段50个关键词
        (30, 100),     # 30个时间段，每段100个关键词
        (50, 200),     # 50个时间段，每段200个关键词
        (100, 500),    # 100个时间段，每段500个关键词
    ]
    
    for num_periods, keywords_per_period in test_cases:
        print(f"\n测试规模: {num_periods} 个时间段 × {keywords_per_period} 个关键词/段")
        
        # 生成测试数据
        print("  生成测试数据...")
        start_time = time.time()
        test_data = generate_test_data(num_periods, keywords_per_period)
        generation_time = time.time() - start_time
        
        total_input_records = sum(len(period['keywords']) for period in test_data)
        print(f"  生成了 {total_input_records:,} 条输入记录，耗时 {generation_time:.3f} 秒")
        
        # 执行合并
        print("  执行数据合并...")
        start_time = time.time()
        result = client._merge_keyword_stats(test_data)
        merge_time = time.time() - start_time
        
        print(f"  合并完成，输出 {len(result):,} 个唯一关键词，耗时 {merge_time:.3f} 秒")
        print(f"  处理速度: {total_input_records / merge_time:,.0f} 记录/秒")
        
        # 验证结果的正确性（抽样检查）
        if result:
            sample_keyword = result[0]
            print(f"  样本关键词: '{sample_keyword.keyword}' - views={sample_keyword.views:,}, clicks={sample_keyword.clicks:,}")
        
        # 内存使用情况（简单估算）
        estimated_memory_mb = (total_input_records * 200 + len(result) * 100) / 1024 / 1024
        print(f"  估算内存使用: {estimated_memory_mb:.2f} MB")
        
        print("  ✅ 测试通过")
    
    print(f"\n{'='*60}")
    print("性能测试总结:")
    print("- 所有规模的数据都能正确处理")
    print("- 处理速度随数据量线性增长")
    print("- 内存使用合理")
    print("- 没有发现性能瓶颈")
    print(f"{'='*60}")


def test_memory_efficiency():
    """内存效率测试"""
    
    client = WildberriesAPIClient("test_api_key")
    
    print(f"\n{'='*60}")
    print("内存效率测试")
    print(f"{'='*60}")
    
    # 测试重复关键词的合并效率
    print("\n测试场景: 大量重复关键词")
    
    # 创建包含大量重复关键词的数据
    test_data = []
    base_keywords = ['热门关键词1', '热门关键词2', '热门关键词3']
    
    for period in range(100):  # 100个时间段
        period_data = {'keywords': []}
        
        for keyword in base_keywords:
            # 每个关键词在每个时间段都出现多次
            for _ in range(50):  # 每个关键词重复50次
                keyword_data = {
                    'keyword': keyword,
                    'views': random.randint(10, 1000),
                    'clicks': random.randint(1, 50),
                    'sum': round(random.uniform(5.0, 100.0), 2),
                    'ctr': round(random.uniform(1.0, 10.0), 2)
                }
                period_data['keywords'].append(keyword_data)
        
        test_data.append(period_data)
    
    total_input_records = sum(len(period['keywords']) for period in test_data)
    print(f"输入记录数: {total_input_records:,}")
    print(f"预期输出关键词数: {len(base_keywords)}")
    
    start_time = time.time()
    result = client._merge_keyword_stats(test_data)
    merge_time = time.time() - start_time
    
    print(f"实际输出关键词数: {len(result)}")
    print(f"合并耗时: {merge_time:.3f} 秒")
    print(f"压缩比: {total_input_records / len(result):,.0f}:1")
    print(f"处理速度: {total_input_records / merge_time:,.0f} 记录/秒")
    
    # 验证数据正确性
    for stats in result:
        expected_records = total_input_records // len(base_keywords)  # 每个关键词的记录数
        print(f"关键词 '{stats.keyword}': views={stats.views:,}, clicks={stats.clicks:,}, ctr={stats.ctr:.2f}")
        
        # 简单验证：views应该是所有记录的累加
        assert stats.views > 0, "views应该大于0"
        assert stats.clicks > 0, "clicks应该大于0"
        assert stats.sum > 0, "sum应该大于0"
    
    print("✅ 内存效率测试通过")


if __name__ == "__main__":
    test_performance()
    test_memory_efficiency()
