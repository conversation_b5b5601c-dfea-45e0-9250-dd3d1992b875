"""
测试关键词统计数据合并功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wb_api_client import WildberriesAPIClient
from models import KeywordStats
from typing import List, Dict, Any


def test_merge_keyword_stats():
    """测试关键词统计数据合并功能"""
    
    # 创建一个测试用的API客户端实例
    client = WildberriesAPIClient("test_api_key")
    
    print("=" * 60)
    print("测试关键词统计数据合并功能")
    print("=" * 60)
    
    # 测试用例1: 标准格式 - 包含keywords字段的字典
    print("\n测试用例1: 标准格式 {'keywords': [...]}")
    test_data_1 = [
        {
            'keywords': [
                {'keyword': '手机壳', 'views': 1000, 'clicks': 50, 'sum': 100.0, 'ctr': 5.0},
                {'keyword': '保护套', 'views': 800, 'clicks': 40, 'sum': 80.0, 'ctr': 5.0}
            ]
        },
        {
            'keywords': [
                {'keyword': '手机壳', 'views': 500, 'clicks': 25, 'sum': 50.0, 'ctr': 5.0},
                {'keyword': '钢化膜', 'views': 300, 'clicks': 15, 'sum': 30.0, 'ctr': 5.0}
            ]
        }
    ]
    
    result_1 = client._merge_keyword_stats(test_data_1)
    print(f"输入数据: {test_data_1}")
    print(f"合并结果: {len(result_1)} 个关键词")
    for stats in result_1:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    
    # 验证结果
    expected_1 = {
        '手机壳': {'views': 1500, 'clicks': 75, 'sum': 150.0, 'ctr': 5.0},
        '保护套': {'views': 800, 'clicks': 40, 'sum': 80.0, 'ctr': 5.0},
        '钢化膜': {'views': 300, 'clicks': 15, 'sum': 30.0, 'ctr': 5.0}
    }
    
    verify_results(result_1, expected_1, "测试用例1")
    
    # 测试用例2: 直接关键词数据格式
    print("\n测试用例2: 直接关键词数据格式 {'keyword': '...', ...}")
    test_data_2 = [
        {'keyword': '充电器', 'views': 600, 'clicks': 30, 'sum': 60.0, 'ctr': 5.0},
        {'keyword': '数据线', 'views': 400, 'clicks': 20, 'sum': 40.0, 'ctr': 5.0},
        {'keyword': '充电器', 'views': 200, 'clicks': 10, 'sum': 20.0, 'ctr': 5.0}
    ]
    
    result_2 = client._merge_keyword_stats(test_data_2)
    print(f"输入数据: {test_data_2}")
    print(f"合并结果: {len(result_2)} 个关键词")
    for stats in result_2:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    
    expected_2 = {
        '充电器': {'views': 800, 'clicks': 40, 'sum': 80.0, 'ctr': 5.0},
        '数据线': {'views': 400, 'clicks': 20, 'sum': 40.0, 'ctr': 5.0}
    }
    
    verify_results(result_2, expected_2, "测试用例2")
    
    # 测试用例3: 混合格式
    print("\n测试用例3: 混合格式")
    test_data_3 = [
        {
            'keywords': [
                {'keyword': '耳机', 'views': 1200, 'clicks': 60, 'sum': 120.0, 'ctr': 5.0}
            ]
        },
        {'keyword': '耳机', 'views': 300, 'clicks': 15, 'sum': 30.0, 'ctr': 5.0},
        {'keyword': '音响', 'views': 500, 'clicks': 25, 'sum': 50.0, 'ctr': 5.0}
    ]
    
    result_3 = client._merge_keyword_stats(test_data_3)
    print(f"输入数据: {test_data_3}")
    print(f"合并结果: {len(result_3)} 个关键词")
    for stats in result_3:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    
    expected_3 = {
        '耳机': {'views': 1500, 'clicks': 75, 'sum': 150.0, 'ctr': 5.0},
        '音响': {'views': 500, 'clicks': 25, 'sum': 50.0, 'ctr': 5.0}
    }
    
    verify_results(result_3, expected_3, "测试用例3")
    
    # 测试用例4: CTR平均值计算
    print("\n测试用例4: CTR平均值计算")
    test_data_4 = [
        {'keyword': '键盘', 'views': 1000, 'clicks': 50, 'sum': 100.0, 'ctr': 5.0},
        {'keyword': '键盘', 'views': 500, 'clicks': 30, 'sum': 50.0, 'ctr': 6.0},
        {'keyword': '键盘', 'views': 200, 'clicks': 10, 'sum': 20.0, 'ctr': 5.0}
    ]
    
    result_4 = client._merge_keyword_stats(test_data_4)
    print(f"输入数据: {test_data_4}")
    print(f"合并结果: {len(result_4)} 个关键词")
    for stats in result_4:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    
    # 验证CTR平均值: (5.0 + 6.0 + 5.0) / 3 = 5.33
    expected_ctr = (5.0 + 6.0 + 5.0) / 3
    print(f"预期CTR平均值: {expected_ctr:.2f}")
    
    # 测试用例5: 空数据和异常数据
    print("\n测试用例5: 空数据和异常数据处理")
    test_data_5 = [
        {'keywords': []},  # 空关键词列表
        {'keyword': '', 'views': 100, 'clicks': 5, 'sum': 10.0, 'ctr': 5.0},  # 空关键词名
        {'keyword': '鼠标', 'views': 200, 'clicks': 10, 'sum': 20.0, 'ctr': 0.0},  # CTR为0
        {'other_field': 'value'},  # 无关字段
        None,  # None值
        "invalid_data"  # 无效数据类型
    ]
    
    result_5 = client._merge_keyword_stats(test_data_5)
    print(f"输入数据: {test_data_5}")
    print(f"合并结果: {len(result_5)} 个关键词")
    for stats in result_5:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")


def verify_results(actual_results: List[KeywordStats], expected: Dict[str, Dict], test_name: str):
    """验证测试结果"""
    print(f"\n验证 {test_name}:")
    
    # 转换实际结果为字典格式便于比较
    actual_dict = {stats.keyword: {
        'views': stats.views,
        'clicks': stats.clicks,
        'sum': stats.sum,
        'ctr': stats.ctr
    } for stats in actual_results}
    
    success = True
    
    # 检查关键词数量
    if len(actual_dict) != len(expected):
        print(f"  ❌ 关键词数量不匹配: 实际 {len(actual_dict)}, 预期 {len(expected)}")
        success = False
    
    # 检查每个关键词的数据
    for keyword, expected_data in expected.items():
        if keyword not in actual_dict:
            print(f"  ❌ 缺少关键词: {keyword}")
            success = False
            continue
            
        actual_data = actual_dict[keyword]
        
        for field in ['views', 'clicks', 'sum', 'ctr']:
            if abs(actual_data[field] - expected_data[field]) > 0.01:  # 允许小数点误差
                print(f"  ❌ {keyword}.{field} 不匹配: 实际 {actual_data[field]}, 预期 {expected_data[field]}")
                success = False
    
    if success:
        print(f"  ✅ {test_name} 通过")
    else:
        print(f"  ❌ {test_name} 失败")
    
    return success


if __name__ == "__main__":
    test_merge_keyword_stats()
