"""
测试关键词统计数据合并的边界情况
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wb_api_client import WildberriesAPIClient
from models import KeywordStats


def test_edge_cases():
    """测试边界情况"""
    
    client = WildberriesAPIClient("test_api_key")
    
    print("=" * 60)
    print("测试关键词统计数据合并的边界情况")
    print("=" * 60)
    
    # 测试用例1: 完全空数据
    print("\n测试用例1: 完全空数据")
    result_1 = client._merge_keyword_stats([])
    print(f"空列表输入，结果: {len(result_1)} 个关键词")
    assert len(result_1) == 0, "空输入应该返回空结果"
    print("✅ 通过")
    
    # 测试用例2: 只有无效数据
    print("\n测试用例2: 只有无效数据")
    result_2 = client._merge_keyword_stats([None, "invalid", 123, {}])
    print(f"无效数据输入，结果: {len(result_2)} 个关键词")
    assert len(result_2) == 0, "无效数据应该返回空结果"
    print("✅ 通过")
    
    # 测试用例3: 数值为负数的情况
    print("\n测试用例3: 数值为负数的情况")
    test_data_3 = [
        {'keyword': '测试', 'views': -100, 'clicks': -10, 'sum': -50.0, 'ctr': -5.0}
    ]
    result_3 = client._merge_keyword_stats(test_data_3)
    print(f"负数输入，结果: {len(result_3)} 个关键词")
    for stats in result_3:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    print("✅ 通过（负数被正确处理）")
    
    # 测试用例4: 极大数值
    print("\n测试用例4: 极大数值")
    test_data_4 = [
        {'keyword': '大数值', 'views': 999999999, 'clicks': 888888888, 'sum': 777777.77, 'ctr': 99.99}
    ]
    result_4 = client._merge_keyword_stats(test_data_4)
    print(f"极大数值输入，结果: {len(result_4)} 个关键词")
    for stats in result_4:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    print("✅ 通过")
    
    # 测试用例5: 特殊字符关键词
    print("\n测试用例5: 特殊字符关键词")
    test_data_5 = [
        {'keyword': '测试@#$%^&*()', 'views': 100, 'clicks': 10, 'sum': 50.0, 'ctr': 10.0},
        {'keyword': '中文关键词', 'views': 200, 'clicks': 20, 'sum': 100.0, 'ctr': 10.0},
        {'keyword': 'English Keyword', 'views': 300, 'clicks': 30, 'sum': 150.0, 'ctr': 10.0}
    ]
    result_5 = client._merge_keyword_stats(test_data_5)
    print(f"特殊字符关键词输入，结果: {len(result_5)} 个关键词")
    for stats in result_5:
        print(f"  '{stats.keyword}': views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    assert len(result_5) == 3, "应该有3个不同的关键词"
    print("✅ 通过")
    
    # 测试用例6: 缺少字段的数据
    print("\n测试用例6: 缺少字段的数据")
    test_data_6 = [
        {'keyword': '缺少字段1', 'views': 100},  # 缺少clicks, sum, ctr
        {'keyword': '缺少字段2', 'clicks': 10, 'sum': 50.0},  # 缺少views, ctr
        {'keyword': '完整字段', 'views': 200, 'clicks': 20, 'sum': 100.0, 'ctr': 10.0}
    ]
    result_6 = client._merge_keyword_stats(test_data_6)
    print(f"缺少字段的数据输入，结果: {len(result_6)} 个关键词")
    for stats in result_6:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
    assert len(result_6) == 3, "应该有3个关键词"
    print("✅ 通过")
    
    # 测试用例7: CTR为0的情况（不应该参与平均值计算）
    print("\n测试用例7: CTR为0的情况")
    test_data_7 = [
        {'keyword': 'CTR测试', 'views': 100, 'clicks': 10, 'sum': 50.0, 'ctr': 0.0},  # CTR为0
        {'keyword': 'CTR测试', 'views': 200, 'clicks': 20, 'sum': 100.0, 'ctr': 5.0},  # CTR为5
        {'keyword': 'CTR测试', 'views': 300, 'clicks': 30, 'sum': 150.0, 'ctr': 10.0}  # CTR为10
    ]
    result_7 = client._merge_keyword_stats(test_data_7)
    print(f"CTR测试输入，结果: {len(result_7)} 个关键词")
    for stats in result_7:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
        # CTR应该是 (5.0 + 10.0) / 2 = 7.5，因为0.0不参与计算
        expected_ctr = (5.0 + 10.0) / 2
        print(f"  预期CTR: {expected_ctr}, 实际CTR: {stats.ctr}")
        assert abs(stats.ctr - expected_ctr) < 0.01, f"CTR计算错误，预期{expected_ctr}，实际{stats.ctr}"
    print("✅ 通过")
    
    # 测试用例8: 所有CTR都为0的情况
    print("\n测试用例8: 所有CTR都为0的情况")
    test_data_8 = [
        {'keyword': '全零CTR', 'views': 100, 'clicks': 10, 'sum': 50.0, 'ctr': 0.0},
        {'keyword': '全零CTR', 'views': 200, 'clicks': 20, 'sum': 100.0, 'ctr': 0.0}
    ]
    result_8 = client._merge_keyword_stats(test_data_8)
    print(f"全零CTR输入，结果: {len(result_8)} 个关键词")
    for stats in result_8:
        print(f"  {stats.keyword}: views={stats.views}, clicks={stats.clicks}, sum={stats.sum}, ctr={stats.ctr}")
        assert stats.ctr == 0.0, "所有CTR为0时，平均CTR应该为0"
    print("✅ 通过")
    
    print("\n" + "=" * 60)
    print("所有边界情况测试通过！")
    print("=" * 60)


def test_real_world_scenario():
    """测试真实世界场景"""
    
    client = WildberriesAPIClient("test_api_key")
    
    print("\n" + "=" * 60)
    print("真实世界场景测试")
    print("=" * 60)
    
    # 模拟真实的API返回数据
    real_world_data = [
        # 第一周数据
        {
            'keywords': [
                {'keyword': '手机壳 苹果', 'views': 1500, 'clicks': 75, 'sum': 225.0, 'ctr': 5.0},
                {'keyword': '保护套 透明', 'views': 800, 'clicks': 32, 'sum': 96.0, 'ctr': 4.0},
                {'keyword': '钢化膜', 'views': 600, 'clicks': 18, 'sum': 54.0, 'ctr': 3.0}
            ]
        },
        # 第二周数据
        {
            'keywords': [
                {'keyword': '手机壳 苹果', 'views': 1200, 'clicks': 84, 'sum': 252.0, 'ctr': 7.0},
                {'keyword': '保护套 透明', 'views': 900, 'clicks': 45, 'sum': 135.0, 'ctr': 5.0},
                {'keyword': '充电线', 'views': 400, 'clicks': 20, 'sum': 60.0, 'ctr': 5.0}
            ]
        },
        # 第三周数据
        {
            'keywords': [
                {'keyword': '手机壳 苹果', 'views': 1800, 'clicks': 108, 'sum': 324.0, 'ctr': 6.0},
                {'keyword': '钢化膜', 'views': 500, 'clicks': 25, 'sum': 75.0, 'ctr': 5.0}
            ]
        }
    ]
    
    result = client._merge_keyword_stats(real_world_data)
    
    print(f"真实场景数据合并结果: {len(result)} 个关键词")
    print("\n详细结果:")
    
    for stats in sorted(result, key=lambda x: x.views, reverse=True):
        print(f"关键词: {stats.keyword}")
        print(f"  总展示: {stats.views:,}")
        print(f"  总点击: {stats.clicks:,}")
        print(f"  总花费: {stats.sum:.2f}")
        print(f"  平均CTR: {stats.ctr:.2f}%")
        print()
    
    # 验证手机壳数据
    phone_case_stats = next((s for s in result if s.keyword == '手机壳 苹果'), None)
    assert phone_case_stats is not None, "应该有手机壳数据"
    
    expected_views = 1500 + 1200 + 1800  # 4500
    expected_clicks = 75 + 84 + 108  # 267
    expected_sum = 225.0 + 252.0 + 324.0  # 801.0
    expected_ctr = (5.0 + 7.0 + 6.0) / 3  # 6.0
    
    assert phone_case_stats.views == expected_views, f"展示数不匹配: {phone_case_stats.views} != {expected_views}"
    assert phone_case_stats.clicks == expected_clicks, f"点击数不匹配: {phone_case_stats.clicks} != {expected_clicks}"
    assert abs(phone_case_stats.sum - expected_sum) < 0.01, f"花费不匹配: {phone_case_stats.sum} != {expected_sum}"
    assert abs(phone_case_stats.ctr - expected_ctr) < 0.01, f"CTR不匹配: {phone_case_stats.ctr} != {expected_ctr}"
    
    print("✅ 真实世界场景测试通过！")


if __name__ == "__main__":
    test_edge_cases()
    test_real_world_scenario()
